const axiosImport = require("axios"); // Import the axios library for making HTTP requests
const { generateKeyPairSync, createSign } = require("crypto"); // Import crypto functions for key generation and signing
const fs = require("fs");

const axios = axiosImport.create({ rejectUnauthorized: false });

// Define API endpoints
const LOGIN_URL =
  "https://idp-per.apps.ocp-nonprod-02.hodomain.local/v3/auth/login"; // Regular login endpoint
const BIOMETRIC_ENROLL =
  "https://idp-per.apps.ocp-nonprod-02.hodomain.local/v3/auth/biometric/enroll"; // Biometric enrollment endpoint
const BIOMETRIC_LOGIN =
  "https://idp-per.apps.ocp-nonprod-02.hodomain.local/v3/auth/biometric/login"; // Biometric login endpoint
const NONCE_URL =
  "https://idp-per.apps.ocp-nonprod-02.hodomain.local/v3/auth/get-nonce"; // Endpoint to fetch a nonce value

const ACCOUNTS_URL =
  "https://api-per.apps.ocp-nonprod-02.hodomain.local/v1/accounts";

const loginPayloads = {};

// Function to fetch a nonce from the server
async function getNonce() {
  try {
    const res = await axios.get(NONCE_URL); // Make a GET request to the nonce endpoint
    const nonce = res.data.nonce; // Extract the nonce from the response data
    return nonce; // Return the nonce value
  } catch (error) {
    console.error("Error fetching nonce:", error); // Log any errors that occur during the process
    throw error; // Re-throw the error to be handled by the calling function
  }
}

// Function to pause execution for a specified time
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms)); // Create a promise that resolves after 'ms' milliseconds
}

// Function to sign a payload using a private key
const signPayload = (payload, privateKey) => {
  const sign = createSign("SHA256"); // Create a signer object using SHA256 algorithm
  sign.update(payload); // Update the signer with the payload
  sign.end(); // Mark the end of the data
  return sign.sign(privateKey, "base64"); // Sign the payload with the private key and return the signature in base64 format
};

// Function to generate a new RSA key pair
function generateKeys() {
  const { publicKey, privateKey } = generateKeyPairSync("rsa", {
    modulusLength: 2048, // Specify the key size (2048 bits)
    publicKeyEncoding: {
      type: "spki", // Use the Subject Public Key Info (SPKI) format
      format: "pem", // Encode the public key in PEM format
    },
    privateKeyEncoding: {
      type: "pkcs8", // Use the Private-Key Cryptography Standards #8 (PKCS#8) format
      format: "pem", // Encode the private key in PEM format
    },
  });
  // Remove PEM header/footer and line breaks to get a single-line base64 string
  const pubKey = publicKey
    .replace("-----BEGIN PUBLIC KEY-----", "") // Remove the beginning header
    .replace("-----END PUBLIC KEY-----", "") // Remove the ending footer
    .replace(/\r?\n|\r/g, "") // Remove any line breaks
    .trim(); // Trim any leading/trailing whitespace

  const prKey = privateKey; // Store the private key

  return {
    publicKey: pubKey, // Return the extracted public key
    privateKey: prKey, // Return the private key
  };
}

// Function to generate a UUID (Universally Unique Identifier)
const generateUuid = () => {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    // Replace each 'x' or 'y' with a random hexadecimal character
    const r = (Math.random() * 16) | 0; // Generate a random number between 0 and 15
    const v = c === "x" ? r : (r & 0x3) | 0x8; // Apply bitmask to ensure the UUID format
    return v.toString(16); // Convert the number to a hexadecimal string
  });
};

// Function to perform biometric login and retrieve a token
async function biometricLoginAndGetToken(payload, nonce, username) {
  const signaturePayload = JSON.stringify({
    cipherKey: payload.response.cipherKey,
    kcUserId: payload.response.kcUserId,
    deviceId: payload.deviceId,
  });

  const loginPayload = {
    deviceId: payload.deviceId,
    payload: `{"cipherKey":"${payload.response.cipherKey}","kcUserId":"${payload.response.kcUserId}","deviceId":"${payload.deviceId}"}`,
    signature: signPayload(signaturePayload, payload.privateKey),
    cipherKey: payload.response.cipherKey,
    kcUserId: payload.response.kcUserId,
    nonce,
  };

  loginPayloads[username] = loginPayload;

  try {
    const res = await axios.post(
      BIOMETRIC_LOGIN,
      { ...loginPayload, nonce },
      {
        headers: {
          "app-version":
            "MIICAQYJKoZIhvcNAQcDoIIB8jCCAe4CAQAxggGnMIIBowIBADCBijByMQswCQYDVQQGEwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTEWMBQGA1UEBwwNU2FuIEZyYW5jaXNjbzEOMAwGA1UECgwFTXlPcmcxDzANBgNVBAsMBk15VW5pdDEVMBMGA1UEAwwMbXlkb21haW4uY29tAhRGy235BTDyUg+WB7SmxtW84G9AtTANBgkqhkiG9w0BAQEFAASCAQA+Sk26YUuRrcmdDk3499493cDiiKmvIabQIdCW7rnp8enT0ZZenYO3X3u41eFP+Tl400nN258/EMp4v2PjCD+gZRMDnc8y8ODI3v0emPt4Fr6J4EMYWhWZppfpF4XV4PhyKgxhjezoHv85WP+OwO9JPLrlOMlUrlHGHWlcMyKJSzShiz14DOvma+u86wYZ5Lrw+9O+wUffVt67/3VH2WDdlw4DuYAreqkRGd0ZiaLBwNr+jyrl7tgO4Efkrcxi6mcY3R7Sc0nm5gvE4ACDlTdASYHyKppExwTF5UAweVEeD4v5BvEObY34trqWynjdCypm4Ka4ykd220NG3Vsrb/FQMD4GCSqGSIb3DQEHATAdBglghkgBZQMEASoEEIE1avqTqsoQIp48H/+wMqqgEgQQzjtEMVtOC1ZEfoi8KBP3Mg==",
          "Content-Type": "application/json",
          login_auth_key: "dds",
          "device-id": payload.deviceId,
        },
      }
    );

    return res.data;
  } catch (err) {
    const res = err.response;
    // Handle AUTH-4 error
    if (res.data && res.data.errorCode === "AUTH-4" && res.data.token) {
      const retryRes = await axios.post(
        BIOMETRIC_LOGIN,
        { ...loginPayload, nonce },
        {
          headers: {
            "app-version":
              "MIICAQYJKoZIhvcNAQcDoIIB8jCCAe4CAQAxggGnMIIBowIBADCBijByMQswCQYDVQQGEwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTEWMBQGA1UEBwwNU2FuIEZyYW5jaXNjbzEOMAwGA1UECgwFTXlPcmcxDzANBgNVBAsMBk15VW5pdDEVMBMGA1UEAwwMbXlkb21haW4uY29tAhRGy235BTDyUg+WB7SmxtW84G9AtTANBgkqhkiG9w0BAQEFAASCAQA+Sk26YUuRrcmdDk3499493cDiiKmvIabQIdCW7rnp8enT0ZZenYO3X3u41eFP+Tl400nN258/EMp4v2PjCD+gZRMDnc8y8ODI3v0emPt4Fr6J4EMYWhWZppfpF4XV4PhyKgxhjezoHv85WP+OwO9JPLrlOMlUrlHGHWlcMyKJSzShiz14DOvma+u86wYZ5Lrw+9O+wUffVt67/3VH2WDdlw4DuYAreqkRGd0ZiaLBwNr+jyrl7tgO4Efkrcxi6mcY3R7Sc0nm5gvE4ACDlTdASYHyKppExwTF5UAweVEeD4v5BvEObY34trqWynjdCypm4Ka4ykd220NG3Vsrb/FQMD4GCSqGSIb3DQEHATAdBglghkgBZQMEASoEEIE1avqTqsoQIp48H/+wMqqgEgQQzjtEMVtOC1ZEfoi8KBP3Mg==",
            "Content-Type": "application/json",
            login_auth_key: "dds",
            "device-id": payload.deviceId,
            Authorization: `Bearer ${res.data.token}`,
          },
        }
      );
      return retryRes.data;
    }
  }
}



const usersList = [ {
  username: "yatwan",
  password: "password",
}];

const loginAndGetToken = (username, nonce) => {
  const loginPayload = {
    username,
    nonce,
    password: "password",
    captchaCode: "213233",
  };

  return axios
    .post(LOGIN_URL, loginPayload)
    .then((response) => {
      return response.data.data.access_token;
    })
    .catch(async (err) => {
      const res = err.response;
      // Handle AUTH-4 error
      if (res.data && res.data.errorCode === "AUTH-4" && res.data.token) {
        const retryRes = await axios.post(
          LOGIN_URL,
          { ...loginPayload, nonce },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${res.data.token}`,
            },
          }
        );
        return retryRes.data.data.access_token;
      }
    });
};

const biometricEnroll = (token, deviceId) => {
  const keys = generateKeys();

  const payload = {
    deviceId: deviceId,
    password: "password",
    publicKey: keys.publicKey,
    otp: "1233",
  };
  console.log("🚀 ~ biometricEnroll ~ payload:", payload)

  return axios
    .post(BIOMETRIC_ENROLL, payload, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    .then((response) => {
      /* 
      {
    "success": true,
    "message": "Biometric login has been enabled.",
    "cipherKey": "e0f35b54dcf6e5eb5bf0bf81c8be380a",
    "kcUserId": "8cf0579b-07d4-4fa3-b03d-8c83813ba8f7",
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIwOGIxMWYyYS0xMmY3LTRlMjItOGQ2Ni05NzljNjI3ZGYyMzUiLCJ1c2VySWQiOiI4NzQ1NjAiLCJ1c2VybmFtZSI6InlhdHdhbiIsImN1c3RvbWVyVHlwZSI6InByaW1lIiwidDI0SWQiOiIxNDAxODM3MDExIiwiY3NyZlRva2VuIjoiREFTWEJhYWJVNENSZW41WkZOU2x5YjVFYzFyZWdjSXFCVXlMWUY0ckRray4iLCJjb29raWVzIjp7ImJ3U2Vzc2lvbklEIjoiNDQ1QjYyQTMtQjYyMi00Q0VFLUE4QTMtRDA2QkI3Mjc0OEZGMSIsImJ3SW5zdGFuY2VObyI6IjEifSwiZHhwMTIzIjoiVlRKR2MyUkhWbXRZTVRsa1lVRXpORkJRUmtkRmRXSjVjVThyUTBkTFZGQk5iV2h0U1ZCclpXazNRVTg1YkM5VVpsWk5jMjV2WjBWc1YyeGtZVXBzT1RCeVJWUXdibXBGVGxodkx6RlpMekZFWlVwRmJHcDNkVlp1T0RReVdscFdWbE5oWms1RVV6bFpjbk05IiwiZHhwMTIzNCI6IlZUSkdjMlJIVm10WU1TdHhhRFZIVkc1Q2MzWnVRM1ZzT1dKelN6VlFSRWhFU1hsaU1qUndSVmhHTjIxR2VWbFhRako0VFdoMFpUUkVaRFZTV2sxUFpGWnpkM2cwVHpWMEsyMXBSbUZCZG5kUWNYQjZUbU5ZV1ZaVFFVbHpUbGR5SzJKUVdtMW5NRkZQV1UwOSIsImlhdCI6MTc0OTk3MzgzMn0.5p9bbwciWbdx7ToZ8vTVS4PQDKqJWHg7Ulpd2qSmRwA",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI4NzQ1NjAiLCJqdGkiOiJmODZmYjFhZi1iMzAxLTRlOTEtYWQ1My0xOWRhZTM5MTViYWMiLCJhY2Nlc3NUb2tlbkpUSSI6IjA4YjExZjJhLTEyZjctNGUyMi04ZDY2LTk3OWM2MjdkZjIzNSIsInVzZXJuYW1lIjoieWF0d2FuIiwiY3VzdG9tZXJUeXBlIjoicHJpbWUiLCJ0MjRJZCI6IjE0MDE4MzcwMTEiLCJkeHAxMjMiOiJWVEpHYzJSSFZtdFlNVGxrWVVFek5GQlFSa2RGZFdKNWNVOHJRMGRMVkZCTmJXaHRTVkJyWldrM1FVODViQzlVWmxaTmMyNXZaMFZzVjJ4a1lVcHNPVEJ5UlZRd2JtcEZUbGh2THpGWkx6RkVaVXBGYkdwM2RWWnVPRFF5V2xwV1ZsTmhaazVFVXpsWmNuTTkiLCJkeHAxMjM0IjoiVlRKR2MyUkhWbXRZTVN0eGFEVkhWRzVDYzNadVEzVnNPV0p6U3pWUVJFaEVTWGxpTWpSd1JWaEdOMjFHZVZsWFFqSjRUV2gwWlRSRVpEVlNXazFQWkZaemQzZzBUelYwSzIxcFJtRkJkbmRRY1hCNlRtTllXVlpUUVVselRsZHlLMkpRV20xbk1GRlBXVTA5IiwiaWF0IjoxNzQ5OTczODMyLCJleHAiOjE3NDk5NzQxMzJ9.oEL3dauHzCr2xY6pSKCr0aKA4Pr16OyDbiUT6htBaqg"
}
      */
      return {
        response: response.data,
        deviceId: payload.deviceId,
        publicKey: payload.publicKey,
        privateKey: keys.privateKey,
      };
    }).catch(err => console.log(err.response.data))
};

(async () => {
  /// For each user in usersList
  for (const user of usersList) {
    const username = user.username;
    sleep(2000); // to avoid throttling
    // Get the nonce for the user
    const nonce = await getNonce();

    // Log in and get the token
    const token = await loginAndGetToken(username, nonce);
    console.log("🚀 ~ token:", token)

    const deviceId = generateUuid();
    console.log("🚀 ~ deviceId:", deviceId)

    if (token) {
      sleep(2000);
      // Biometric enroll
      const enrollmentResponse = await biometricEnroll(token, deviceId);

      sleep(2000);
      const biometricLoginResponse = await biometricLoginAndGetToken(
        enrollmentResponse,
        nonce,
        username
      );

      fs.writeFileSync(
        "loginPayloads2.json",
        JSON.stringify(loginPayloads, null, 2)
      );

      console.log("added ", user.username);

      // axios
      //   .get(ACCOUNTS_URL, {
      //     headers: {
      //       "device-id": deviceId,
      //       "app-version":
      //         "MIICAQYJKoZIhvcNAQcDoIIB8jCCAe4CAQAxggGnMIIBowIBADCBijByMQswCQYDVQQGEwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTEWMBQGA1UEBwwNU2FuIEZyYW5jaXNjbzEOMAwGA1UECgwFTXlPcmcxDzANBgNVBAsMBk15VW5pdDEVMBMGA1UEAwwMbXlkb21haW4uY29tAhRGy235BTDyUg+WB7SmxtW84G9AtTANBgkqhkiG9w0BAQEFAASCAQA+Sk26YUuRrcmdDk3499493cDiiKmvIabQIdCW7rnp8enT0ZZenYO3X3u41eFP+Tl400nN258/EMp4v2PjCD+gZRMDnc8y8ODI3v0emPt4Fr6J4EMYWhWZppfpF4XV4PhyKgxhjezoHv85WP+OwO9JPLrlOMlUrlHGHWlcMyKJSzShiz14DOvma+u86wYZ5Lrw+9O+wUffVt67/3VH2WDdlw4DuYAreqkRGd0ZiaLBwNr+jyrl7tgO4Efkrcxi6mcY3R7Sc0nm5gvE4ACDlTdASYHyKppExwTF5UAweVEeD4v5BvEObY34trqWynjdCypm4Ka4ykd220NG3Vsrb/FQMD4GCSqGSIb3DQEHATAdBglghkgBZQMEASoEEIE1avqTqsoQIp48H/+wMqqgEgQQzjtEMVtOC1ZEfoi8KBP3Mg==",
      //       login_auth_key: "mobile",
      //       Authorization: `Bearer ${biometricLoginResponse.data.access_token}`,
      //     },
      //   })
      //   .then((res) => {
      //     console.log("🚀 ~ .then ~ res:", res.data);
      //   })
      //   .catch((err) => {
      //     console.error(
      //       "🚀 ~ .catch ~ err:",
      //       err.response?.status || err.message
      //     );
      //   });
    }
  }
})();
